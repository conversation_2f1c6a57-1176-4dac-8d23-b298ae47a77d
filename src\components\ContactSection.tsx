import React from 'react';
import { Box, Container, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardContent, But<PERSON>, Divider } from '@mui/material';
import { styled } from '@mui/material/styles';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import WebIcon from '@mui/icons-material/Web';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PersonIcon from '@mui/icons-material/Person';
import WorkIcon from '@mui/icons-material/Work';
import LanguageIcon from '@mui/icons-material/Language';

const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(12, 0),
  background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}15 100%)`,
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 30% 20%, ${theme.palette.primary.main}10 0%, transparent 50%),
                 radial-gradient(circle at 70% 80%, ${theme.palette.secondary.main}10 0%, transparent 50%)`,
  },
}));

const ContactCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  transition: 'all 0.3s ease',
  position: 'relative',
  zIndex: 1,
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
    background: 'rgba(255, 255, 255, 0.95)',
  },
}));

const ContactButton = styled(Button)(({ theme }) => ({
  borderRadius: '12px',
  padding: theme.spacing(1.5, 3),
  textTransform: 'none',
  fontWeight: 500,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
  },
}));

const InfoItem = styled(Stack)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: '12px',
  background: 'rgba(255, 255, 255, 0.5)',
  border: '1px solid rgba(255, 255, 255, 0.3)',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'rgba(255, 255, 255, 0.8)',
    transform: 'translateY(-2px)',
  },
}));

const ContactSection: React.FC = () => {
  const contactMethods = [
    {
      icon: <EmailIcon sx={{ fontSize: 28, color: 'primary.main' }} />,
      label: 'Email',
      value: '<EMAIL>',
      action: 'mailto:<EMAIL>',
      buttonText: 'Send Email',
      primary: true
    },
    {
      icon: <PhoneIcon sx={{ fontSize: 28, color: 'secondary.main' }} />,
      label: 'Phone',
      value: '+965-51531519',
      action: 'tel:+96551531519',
      buttonText: 'Call Now',
      primary: false
    },
    {
      icon: <LinkedInIcon sx={{ fontSize: 28, color: 'info.main' }} />,
      label: 'LinkedIn',
      value: 'Professional Profile',
      action: 'https://linkedin.com/in/muhammad-faizan-muhammad-hanif-51403a65/',
      buttonText: 'Connect',
      primary: false
    },
    {
      icon: <WebIcon sx={{ fontSize: 28, color: 'primary.main' }} />,
      label: 'Blog',
      value: 'mcsaguru.com',
      action: 'https://mcsaguru.com',
      buttonText: 'Visit Blog',
      primary: false
    }
  ];

  const personalInfo = [
    {
      icon: <PersonIcon sx={{ fontSize: 24, color: 'text.secondary' }} />,
      label: 'Full Name',
      value: 'Muhammad Faizan Hanif'
    },
    {
      icon: <LocationOnIcon sx={{ fontSize: 24, color: 'text.secondary' }} />,
      label: 'Location',
      value: 'Kuwait'
    },
    {
      icon: <WorkIcon sx={{ fontSize: 24, color: 'text.secondary' }} />,
      label: 'Experience',
      value: '17+ Years in Kuwait'
    },
    {
      icon: <LanguageIcon sx={{ fontSize: 24, color: 'text.secondary' }} />,
      label: 'Nationality',
      value: 'Pakistani'
    }
  ];

  return (
    <SectionContainer>
      <Container maxWidth="lg">
        <Stack spacing={8}>
          {/* Header */}
          <Stack spacing={3} textAlign="center">
            <Typography variant="h2" color="text.primary" fontWeight="bold">
              Let's Connect
            </Typography>
            <Typography 
              variant="h6" 
              color="text.secondary" 
              sx={{ maxWidth: 600, mx: 'auto', lineHeight: 1.6 }}
            >
              Ready to discuss your IT infrastructure needs or explore collaboration opportunities? 
              I'm always open to connecting with fellow professionals.
            </Typography>
          </Stack>

          <Stack direction={{ xs: 'column', lg: 'row' }} spacing={6}>
            {/* Contact Methods */}
            <Stack spacing={4} flex={1}>
              <Typography variant="h4" fontWeight="bold" textAlign="center">
                Get In Touch
              </Typography>
              
              <Stack spacing={3}>
                {contactMethods.map((method, index) => (
                  <ContactCard key={index}>
                    <CardContent sx={{ p: 4 }}>
                      <Stack direction="row" spacing={3} alignItems="center">
                        <Box
                          sx={{
                            width: 60,
                            height: 60,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            background: method.primary 
                              ? 'linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(124, 58, 237, 0.1))'
                              : 'rgba(0, 0, 0, 0.05)',
                          }}
                        >
                          {method.icon}
                        </Box>
                        
                        <Stack spacing={1} flex={1}>
                          <Typography variant="h6" fontWeight="bold">
                            {method.label}
                          </Typography>
                          <Typography variant="body1" color="text.secondary">
                            {method.value}
                          </Typography>
                        </Stack>
                        
                        <ContactButton
                          variant={method.primary ? "contained" : "outlined"}
                          href={method.action}
                          target={method.action.startsWith('http') ? '_blank' : undefined}
                          sx={method.primary ? {
                            background: 'linear-gradient(135deg, #2563EB, #7C3AED)',
                            '&:hover': {
                              boxShadow: '0 10px 25px rgba(37, 99, 235, 0.3)',
                            },
                          } : {
                            borderColor: 'primary.main',
                            color: 'primary.main',
                            '&:hover': {
                              background: 'rgba(37, 99, 235, 0.1)',
                            },
                          }}
                        >
                          {method.buttonText}
                        </ContactButton>
                      </Stack>
                    </CardContent>
                  </ContactCard>
                ))}
              </Stack>
            </Stack>

            {/* Personal Information */}
            <Stack spacing={4} flex={1}>
              <Typography variant="h4" fontWeight="bold" textAlign="center">
                Professional Details
              </Typography>
              
              <ContactCard>
                <CardContent sx={{ p: 4 }}>
                  <Stack spacing={3}>
                    {personalInfo.map((info, index) => (
                      <React.Fragment key={index}>
                        <InfoItem direction="row" spacing={2} alignItems="center">
                          {info.icon}
                          <Stack flex={1}>
                            <Typography variant="body2" color="text.secondary">
                              {info.label}
                            </Typography>
                            <Typography variant="body1" fontWeight="medium">
                              {info.value}
                            </Typography>
                          </Stack>
                        </InfoItem>
                        {index < personalInfo.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </Stack>
                </CardContent>
              </ContactCard>

              {/* Additional Info */}
              <ContactCard>
                <CardContent sx={{ p: 4 }}>
                  <Stack spacing={3}>
                    <Typography variant="h6" fontWeight="bold" textAlign="center">
                      Current Status
                    </Typography>
                    
                    <Stack spacing={2}>
                      <InfoItem>
                        <Typography variant="body2" color="text.secondary">
                          Position
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          Senior Network/System Administrator
                        </Typography>
                      </InfoItem>
                      
                      <InfoItem>
                        <Typography variant="body2" color="text.secondary">
                          Organization
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          American International School of Kuwait
                        </Typography>
                      </InfoItem>
                      
                      <InfoItem>
                        <Typography variant="body2" color="text.secondary">
                          Availability
                        </Typography>
                        <Typography variant="body1" fontWeight="medium" color="primary.main">
                          Open to new opportunities
                        </Typography>
                      </InfoItem>
                    </Stack>
                  </Stack>
                </CardContent>
              </ContactCard>
            </Stack>
          </Stack>

          {/* Call to Action */}
          <Card 
            sx={{ 
              background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(124, 58, 237, 0.1))',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '20px'
            }}
          >
            <CardContent sx={{ p: 6 }}>
              <Stack spacing={4} textAlign="center">
                <Typography variant="h4" fontWeight="bold">
                  Ready to Transform Your IT Infrastructure?
                </Typography>
                
                <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 800, mx: 'auto', lineHeight: 1.8 }}>
                  With 17+ years of proven experience in enterprise environments and a track record of 
                  99.99% uptime, I'm ready to bring innovation, reliability, and AI-powered solutions 
                  to your organization. Let's discuss how we can elevate your IT infrastructure together.
                </Typography>
                
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} justifyContent="center">
                  <ContactButton
                    variant="contained"
                    size="large"
                    startIcon={<EmailIcon />}
                    href="mailto:<EMAIL>"
                    sx={{
                      background: 'linear-gradient(135deg, #2563EB, #7C3AED)',
                      px: 4,
                      py: 2,
                      fontSize: '1.1rem',
                      '&:hover': {
                        boxShadow: '0 15px 35px rgba(37, 99, 235, 0.4)',
                      },
                    }}
                  >
                    Start the Conversation
                  </ContactButton>
                  
                  <ContactButton
                    variant="outlined"
                    size="large"
                    startIcon={<WebIcon />}
                    href="https://portfolio.mcsaguru.com"
                    target="_blank"
                    sx={{
                      borderColor: 'primary.main',
                      color: 'primary.main',
                      px: 4,
                      py: 2,
                      fontSize: '1.1rem',
                      '&:hover': {
                        background: 'rgba(37, 99, 235, 0.1)',
                      },
                    }}
                  >
                    View Full Portfolio
                  </ContactButton>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    </SectionContainer>
  );
};

export default ContactSection;