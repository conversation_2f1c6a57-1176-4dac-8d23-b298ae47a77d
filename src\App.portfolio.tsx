import React from 'react';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import theme from './theme/theme';
import HeroSection from './components/HeroSection';
import AISection from './components/AISection';
import SkillsSection from './components/SkillsSection';
import ExperienceSection from './components/ExperienceSection';
import PortfolioSection from './components/PortfolioSection';
import ContactSection from './components/ContactSection';

const createEmotionCache = () => {
  return createCache({
    key: "mui",
    prepend: true,
  });
};

const emotionCache = createEmotionCache();

const App: React.FC = () => {
  return (
    <CacheProvider value={emotionCache}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div>
          <HeroSection />
          <AISection />
          <SkillsSection />
          <ExperienceSection />
          <PortfolioSection />
          <ContactSection />
        </div>
      </ThemeProvider>
    </CacheProvider>
  );
};

export default App;