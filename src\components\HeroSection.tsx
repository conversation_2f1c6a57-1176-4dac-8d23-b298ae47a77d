import React from 'react';
import { Box, Container, Typography, <PERSON>ack, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import EmailIcon from '@mui/icons-material/Email';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import WebIcon from '@mui/icons-material/Web';

const HeroContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}15 100%)`,
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `radial-gradient(circle at 20% 50%, ${theme.palette.primary.main}20 0%, transparent 50%),
                 radial-gradient(circle at 80% 20%, ${theme.palette.secondary.main}20 0%, transparent 50%),
                 radial-gradient(circle at 40% 80%, ${theme.palette.info.main}15 0%, transparent 50%)`,
    animation: 'float 20s ease-in-out infinite',
  },
  '@keyframes float': {
    '0%, 100%': {
      transform: 'translateY(0px) rotate(0deg)',
    },
    '50%': {
      transform: 'translateY(-20px) rotate(1deg)',
    },
  },
}));

const GlassCard = styled(Box)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '24px',
  padding: theme.spacing(6),
  position: 'relative',
  zIndex: 1,
  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
}));

const AnimatedText = styled(Typography)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  backgroundClip: 'text',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  animation: 'textShine 3s ease-in-out infinite',
  '@keyframes textShine': {
    '0%, 100%': {
      backgroundPosition: '0% 50%',
    },
    '50%': {
      backgroundPosition: '100% 50%',
    },
  },
}));

const StatsCard = styled(Box)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.05)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.1)',
  borderRadius: '16px',
  padding: theme.spacing(3),
  textAlign: 'center',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    background: 'rgba(255, 255, 255, 0.1)',
  },
}));

const HeroSection: React.FC = () => {
  return (
    <HeroContainer>
      <Container maxWidth="lg">
        <Stack spacing={6} alignItems="center" textAlign="center">
          <GlassCard>
            <Stack spacing={4} alignItems="center">
              <AnimatedText variant="h1" component="h1">
                Muhammad Faizan Hanif
              </AnimatedText>
              
              <Typography 
                variant="h4" 
                color="text.primary" 
                sx={{ fontWeight: 400, opacity: 0.9 }}
              >
                Senior Network/System Administrator
              </Typography>
              
              <Typography 
                variant="h6" 
                color="text.secondary" 
                sx={{ maxWidth: 600, lineHeight: 1.6 }}
              >
                Transforming IT Infrastructure with 17+ Years of Excellence in Kuwait
                <br />
                Pioneering AI Integration in Enterprise Systems
              </Typography>

              <Stack direction="row" spacing={2} flexWrap="wrap" justifyContent="center">
                <Button
                  variant="contained"
                  startIcon={<EmailIcon />}
                  href="mailto:<EMAIL>"
                  sx={{
                    background: 'linear-gradient(135deg, #2563EB, #7C3AED)',
                    borderRadius: '12px',
                    px: 3,
                    py: 1.5,
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 10px 25px rgba(37, 99, 235, 0.3)',
                    },
                  }}
                >
                  Contact Me
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<LinkedInIcon />}
                  href="https://linkedin.com/in/muhammad-faizan-muhammad-hanif-51403a65/"
                  target="_blank"
                  sx={{
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    color: 'text.primary',
                    borderRadius: '12px',
                    px: 3,
                    py: 1.5,
                    '&:hover': {
                      borderColor: 'primary.main',
                      background: 'rgba(37, 99, 235, 0.1)',
                      transform: 'translateY(-2px)',
                    },
                  }}
                >
                  LinkedIn
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<WebIcon />}
                  href="https://mcsaguru.com"
                  target="_blank"
                  sx={{
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    color: 'text.primary',
                    borderRadius: '12px',
                    px: 3,
                    py: 1.5,
                    '&:hover': {
                      borderColor: 'secondary.main',
                      background: 'rgba(124, 58, 237, 0.1)',
                      transform: 'translateY(-2px)',
                    },
                  }}
                >
                  Blog
                </Button>
              </Stack>
            </Stack>
          </GlassCard>

          <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ mt: 4 }}>
            <StatsCard>
              <Typography variant="h3" color="primary.main" fontWeight="bold">
                17+
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Years Experience
              </Typography>
            </StatsCard>
            
            <StatsCard>
              <Typography variant="h3" color="secondary.main" fontWeight="bold">
                4000+
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Users Managed
              </Typography>
            </StatsCard>
            
            <StatsCard>
              <Typography variant="h3" color="info.main" fontWeight="bold">
                99.99%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                System Uptime
              </Typography>
            </StatsCard>
            
            <StatsCard>
              <Typography variant="h3" color="primary.main" fontWeight="bold">
                400+
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Security Cameras
              </Typography>
            </StatsCard>
          </Stack>
        </Stack>
      </Container>
    </HeroContainer>
  );
};

export default HeroSection;