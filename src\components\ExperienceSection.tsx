import React, { useState } from 'react';
import { Box, Container, Typography, <PERSON><PERSON>, Card, CardContent, Chip, Collapse, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SchoolIcon from '@mui/icons-material/School';
import BusinessIcon from '@mui/icons-material/Business';
import EngineeringIcon from '@mui/icons-material/Engineering';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(12, 0),
  background: `linear-gradient(180deg, ${theme.palette.grey[50]} 0%, ${theme.palette.background.default} 100%)`,
}));

const TimelineContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    left: '50%',
    top: 0,
    bottom: 0,
    width: '4px',
    background: `linear-gradient(180deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    transform: 'translateX(-50%)',
    borderRadius: '2px',
    [theme.breakpoints.down('md')]: {
      left: '30px',
    },
  },
}));

const TimelineItem = styled(Box)<{ isLeft?: boolean }>(({ theme, isLeft }) => ({
  display: 'flex',
  justifyContent: isLeft ? 'flex-end' : 'flex-start',
  alignItems: 'center',
  marginBottom: theme.spacing(6),
  position: 'relative',
  [theme.breakpoints.down('md')]: {
    justifyContent: 'flex-start',
    paddingLeft: theme.spacing(8),
  },
}));

const TimelineIcon = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: '50%',
  transform: 'translateX(-50%)',
  width: 60,
  height: 60,
  borderRadius: '50%',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: '0 10px 30px rgba(37, 99, 235, 0.3)',
  zIndex: 2,
  [theme.breakpoints.down('md')]: {
    left: '30px',
  },
}));

const ExperienceCard = styled(Card)<{ isExpanded?: boolean }>(({ theme, isExpanded }) => ({
  maxWidth: 500,
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  border: isExpanded 
    ? `2px solid ${theme.palette.primary.main}`
    : '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  transition: 'all 0.3s ease',
  transform: isExpanded ? 'scale(1.02)' : 'scale(1)',
  boxShadow: isExpanded 
    ? '0 20px 40px rgba(37, 99, 235, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.1)',
}));

const ExpandButton = styled(IconButton)<{ expanded?: boolean }>(({ theme, expanded }) => ({
  transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
  transition: 'transform 0.3s ease',
  color: theme.palette.primary.main,
}));

const AchievementChip = styled(Chip)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,
  border: `1px solid ${theme.palette.primary.main}30`,
  color: theme.palette.primary.main,
  fontWeight: 500,
  '&:hover': {
    background: `linear-gradient(135deg, ${theme.palette.primary.main}25, ${theme.palette.secondary.main}25)`,
  },
}));

const ExperienceSection: React.FC = () => {
  const [expandedCard, setExpandedCard] = useState<number | null>(null);

  const experiences = [
    {
      icon: <SchoolIcon sx={{ fontSize: 30, color: 'white' }} />,
      company: 'American International School of Kuwait',
      position: 'Senior Systems & Network Administrator',
      duration: 'June 2015 - Present (9+ Years)',
      location: 'Kuwait',
      summary: 'Leading IT infrastructure for one of Kuwait\'s premier educational institutions, managing 4000+ users with 99.99% uptime.',
      responsibilities: [
        'Architect and maintain enterprise Windows Server infrastructure (2008-2022)',
        'Design hybrid mail infrastructure with Exchange and Office 365 integration',
        'Lead virtualization initiatives across VMware vSphere, Hyper-V, and Proxmox',
        'Manage comprehensive network infrastructure with Cisco switching',
        'Oversee enterprise security with Fortigate firewall clusters',
        'Administer 400+ Hikvision IP cameras surveillance system'
      ],
      achievements: [
        '75% reduction in system incidents through automation',
        '40% reduction in IT operational costs',
        'Zero major security incidents over 5 years',
        '99.99% uptime for critical systems',
        'Managed growth from 1000 to 4000+ users',
        'Saved 100+ hours monthly through automation'
      ],
      technologies: ['Windows Server', 'VMware vSphere', 'Exchange', 'Office 365', 'FortiGate', 'Cisco', 'Python', 'PowerShell']
    },
    {
      icon: <BusinessIcon sx={{ fontSize: 30, color: 'white' }} />,
      company: 'Computer Troubleshooters Kuwait',
      position: 'Network/System Administrator',
      duration: 'November 2009 - June 2015 (5+ Years)',
      location: 'Kuwait',
      summary: 'Provided comprehensive IT services for small to medium businesses and large organizations as part of world\'s largest computer services franchise.',
      responsibilities: [
        'Managed and troubleshot domain controllers',
        'Provided technical support for servers and workstations',
        'Administered DHCP, WINS, DNS, file and print servers',
        'Created user accounts, permissions, and group memberships',
        'Established network specifications and performance monitoring',
        'Conducted training programs for end users'
      ],
      achievements: [
        'Successfully managed multiple client environments',
        'Implemented network security best practices',
        'Reduced client downtime through proactive monitoring',
        'Established efficient backup and recovery procedures'
      ],
      technologies: ['Windows Server', 'Active Directory', 'Network Security', 'DHCP/DNS', 'Backup Solutions', 'User Training']
    },
    {
      icon: <EngineeringIcon sx={{ fontSize: 30, color: 'white' }} />,
      company: 'Kellogg Brown and Root (KBR)',
      position: 'Network/System Administrator',
      duration: 'November 2007 - September 2009 (2 Years)',
      location: 'Kuwait',
      summary: 'Managed network operations for one of the world\'s premier engineering and construction companies with 27,000+ employees globally.',
      responsibilities: [
        'Supervised daily network operations and security',
        'Managed internet/intranet administration and backup systems',
        'Implemented integrated LAN/WAN system designs',
        'Monitored network performance, speed, and reliability',
        'Installed and configured routers, switches, and hubs',
        'Provided Level III support to technical staff'
      ],
      achievements: [
        'Maintained high network availability',
        'Successfully managed complex LAN/WAN infrastructure',
        'Trained junior technicians and support staff',
        'Resolved critical network performance issues'
      ],
      technologies: ['LAN/WAN', 'Routers & Switches', 'Network Security', 'Citrix', 'NT/Netware', 'Backup Systems']
    }
  ];

  const handleExpandClick = (index: number) => {
    setExpandedCard(expandedCard === index ? null : index);
  };

  return (
    <SectionContainer>
      <Container maxWidth="lg">
        <Stack spacing={8}>
          {/* Header */}
          <Stack spacing={3} textAlign="center">
            <Typography variant="h2" color="text.primary" fontWeight="bold">
              Professional Experience
            </Typography>
            <Typography 
              variant="h6" 
              color="text.secondary" 
              sx={{ maxWidth: 800, mx: 'auto', lineHeight: 1.6 }}
            >
              17+ years of progressive experience in enterprise IT environments, 
              consistently delivering innovative solutions and exceptional results.
            </Typography>
          </Stack>

          {/* Timeline */}
          <TimelineContainer>
            {experiences.map((exp, index) => (
              <TimelineItem key={index} isLeft={index % 2 === 0}>
                <TimelineIcon>
                  {exp.icon}
                </TimelineIcon>
                
                <ExperienceCard isExpanded={expandedCard === index}>
                  <CardContent sx={{ p: 4 }}>
                    <Stack spacing={3}>
                      {/* Header */}
                      <Stack spacing={1}>
                        <Typography variant="h5" fontWeight="bold" color="text.primary">
                          {exp.position}
                        </Typography>
                        <Typography variant="h6" color="primary.main" fontWeight="medium">
                          {exp.company}
                        </Typography>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Typography variant="body2" color="text.secondary">
                            {exp.duration}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            • {exp.location}
                          </Typography>
                        </Stack>
                      </Stack>

                      {/* Summary */}
                      <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                        {exp.summary}
                      </Typography>

                      {/* Technologies */}
                      <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                        {exp.technologies.slice(0, 4).map((tech, techIndex) => (
                          <Chip
                            key={techIndex}
                            label={tech}
                            size="small"
                            sx={{
                              backgroundColor: 'rgba(37, 99, 235, 0.1)',
                              color: 'primary.main',
                              border: '1px solid rgba(37, 99, 235, 0.3)',
                            }}
                          />
                        ))}
                        {exp.technologies.length > 4 && (
                          <Chip
                            label={`+${exp.technologies.length - 4} more`}
                            size="small"
                            sx={{
                              backgroundColor: 'rgba(124, 58, 237, 0.1)',
                              color: 'secondary.main',
                              border: '1px solid rgba(124, 58, 237, 0.3)',
                            }}
                          />
                        )}
                      </Stack>

                      {/* Expand Button */}
                      <Stack direction="row" justifyContent="center">
                        <ExpandButton
                          expanded={expandedCard === index}
                          onClick={() => handleExpandClick(index)}
                        >
                          <ExpandMoreIcon />
                        </ExpandButton>
                      </Stack>

                      {/* Expanded Content */}
                      <Collapse in={expandedCard === index}>
                        <Stack spacing={3}>
                          {/* Responsibilities */}
                          <Stack spacing={2}>
                            <Typography variant="h6" color="text.primary" fontWeight="bold">
                              Key Responsibilities
                            </Typography>
                            <Stack spacing={1}>
                              {exp.responsibilities.map((resp, respIndex) => (
                                <Typography 
                                  key={respIndex}
                                  variant="body2" 
                                  color="text.secondary"
                                  sx={{ 
                                    display: 'flex',
                                    '&::before': {
                                      content: '"•"',
                                      marginRight: 1,
                                      color: 'primary.main',
                                      fontWeight: 'bold'
                                    }
                                  }}
                                >
                                  {resp}
                                </Typography>
                              ))}
                            </Stack>
                          </Stack>

                          {/* Achievements */}
                          <Stack spacing={2}>
                            <Typography variant="h6" color="text.primary" fontWeight="bold">
                              Key Achievements
                            </Typography>
                            <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                              {exp.achievements.map((achievement, achIndex) => (
                                <AchievementChip
                                  key={achIndex}
                                  label={achievement}
                                  size="small"
                                />
                              ))}
                            </Stack>
                          </Stack>

                          {/* All Technologies */}
                          <Stack spacing={2}>
                            <Typography variant="h6" color="text.primary" fontWeight="bold">
                              Technologies Used
                            </Typography>
                            <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                              {exp.technologies.map((tech, techIndex) => (
                                <Chip
                                  key={techIndex}
                                  label={tech}
                                  size="small"
                                  variant="outlined"
                                  sx={{
                                    borderColor: 'primary.main',
                                    color: 'primary.main',
                                    '&:hover': {
                                      backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                    }
                                  }}
                                />
                              ))}
                            </Stack>
                          </Stack>
                        </Stack>
                      </Collapse>
                    </Stack>
                  </CardContent>
                </ExperienceCard>
              </TimelineItem>
            ))}
          </TimelineContainer>

          {/* Summary Stats */}
          <Card 
            sx={{ 
              background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(124, 58, 237, 0.1))',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '20px'
            }}
          >
            <CardContent sx={{ p: 6 }}>
              <Typography variant="h4" fontWeight="bold" gutterBottom textAlign="center">
                Career Highlights
              </Typography>
              
              <Stack 
                direction={{ xs: 'column', md: 'row' }} 
                spacing={4} 
                sx={{ mt: 4 }}
                divider={<Box sx={{ width: '1px', bgcolor: 'divider', height: '100px', display: { xs: 'none', md: 'block' } }} />}
              >
                <Stack spacing={2} flex={1} textAlign="center">
                  <TrendingUpIcon sx={{ fontSize: 48, color: 'primary.main', mx: 'auto' }} />
                  <Typography variant="h3" color="primary.main" fontWeight="bold">
                    17+
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Years of Experience
                  </Typography>
                </Stack>
                
                <Stack spacing={2} flex={1} textAlign="center">
                  <SchoolIcon sx={{ fontSize: 48, color: 'secondary.main', mx: 'auto' }} />
                  <Typography variant="h3" color="secondary.main" fontWeight="bold">
                    4000+
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Users Managed
                  </Typography>
                </Stack>
                
                <Stack spacing={2} flex={1} textAlign="center">
                  <EngineeringIcon sx={{ fontSize: 48, color: 'info.main', mx: 'auto' }} />
                  <Typography variant="h3" color="info.main" fontWeight="bold">
                    99.99%
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    System Uptime
                  </Typography>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    </SectionContainer>
  );
};

export default ExperienceSection;