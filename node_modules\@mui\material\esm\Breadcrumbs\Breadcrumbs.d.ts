import * as React from 'react';
import { SxProps } from '@mui/system';
import { SlotComponentProps } from "../utils/types.js";
import { Theme } from "../styles/index.js";
import { OverridableComponent, OverrideProps } from "../OverridableComponent/index.js";
import { BreadcrumbsClasses } from "./breadcrumbsClasses.js";
import SvgIcon from "../SvgIcon/index.js";
export interface BreadcrumbsCollapsedIconSlotPropsOverrides {}
export interface BreadcrumbsOwnerState extends BreadcrumbsProps {
  expanded: boolean;
}
export interface BreadcrumbsOwnProps {
  /**
   * The content of the component.
   */
  children?: React.ReactNode;
  /**
   * Override or extend the styles applied to the component.
   */
  classes?: Partial<BreadcrumbsClasses>;
  /**
   * The components used for each slot inside the Breadcumb.
   * Either a string to use a HTML element or a component.
   * @default {}
   */
  slots?: {
    CollapsedIcon?: React.ElementType;
  };
  /**
   * The props used for each slot inside the Breadcumb.
   * @default {}
   */
  slotProps?: {
    /**
     * Props applied to the CollapsedIcon slot.
     * @default {}
     */
    collapsedIcon?: SlotComponentProps<typeof SvgIcon, BreadcrumbsCollapsedIconSlotPropsOverrides, BreadcrumbsOwnerState>;
  };
  /**
   * Override the default label for the expand button.
   *
   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).
   * @default 'Show path'
   */
  expandText?: string;
  /**
   * If max items is exceeded, the number of items to show after the ellipsis.
   * @default 1
   */
  itemsAfterCollapse?: number;
  /**
   * If max items is exceeded, the number of items to show before the ellipsis.
   * @default 1
   */
  itemsBeforeCollapse?: number;
  /**
   * Specifies the maximum number of breadcrumbs to display. When there are more
   * than the maximum number, only the first `itemsBeforeCollapse` and last `itemsAfterCollapse`
   * will be shown, with an ellipsis in between.
   * @default 8
   */
  maxItems?: number;
  /**
   * Custom separator node.
   * @default '/'
   */
  separator?: React.ReactNode;
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx?: SxProps<Theme>;
}
export interface BreadcrumbsTypeMap<AdditionalProps = {}, RootComponent extends React.ElementType = 'nav'> {
  props: AdditionalProps & BreadcrumbsOwnProps;
  defaultComponent: RootComponent;
}

/**
 *
 * Demos:
 *
 * - [Breadcrumbs](https://mui.com/material-ui/react-breadcrumbs/)
 *
 * API:
 *
 * - [Breadcrumbs API](https://mui.com/material-ui/api/breadcrumbs/)
 * - inherits [Typography API](https://mui.com/material-ui/api/typography/)
 */
declare const Breadcrumbs: OverridableComponent<BreadcrumbsTypeMap>;
export type BreadcrumbsProps<RootComponent extends React.ElementType = BreadcrumbsTypeMap['defaultComponent'], AdditionalProps = {}> = OverrideProps<BreadcrumbsTypeMap<AdditionalProps, RootComponent>, RootComponent> & {
  component?: React.ElementType;
};
export default Breadcrumbs;