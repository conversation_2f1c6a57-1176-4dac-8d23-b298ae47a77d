import React from 'react';
import { Box, Container, Typography, Stack, Card, CardContent } from '@mui/material';
import { styled } from '@mui/material/styles';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import DataObjectIcon from '@mui/icons-material/DataObject';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(12, 0),
  background: `linear-gradient(180deg, ${theme.palette.background.default} 0%, ${theme.palette.grey[50]} 100%)`,
}));

const AICard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.8)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  transition: 'all 0.3s ease',
  height: '100%',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
    background: 'rgba(255, 255, 255, 0.95)',
  },
}));

const CodeBlock = styled(Box)(({ theme }) => ({
  background: theme.palette.grey[900],
  color: theme.palette.common.white,
  padding: theme.spacing(3),
  borderRadius: '12px',
  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
  fontSize: '0.875rem',
  lineHeight: 1.6,
  overflow: 'auto',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '3px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
    borderRadius: '12px 12px 0 0',
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  width: 64,
  height: 64,
  borderRadius: '16px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}20, ${theme.palette.secondary.main}20)`,
  marginBottom: theme.spacing(2),
}));

const AISection: React.FC = () => {
  const aiTools = [
    {
      icon: <SmartToyIcon sx={{ fontSize: 32, color: 'primary.main' }} />,
      title: 'AI-Based Scripts & Automation',
      description: 'Created Python scripts using machine learning to predict server issues and built automation tools that learn from network patterns.',
      features: ['Predictive server monitoring', 'Self-adjusting network settings', 'Pattern recognition algorithms']
    },
    {
      icon: <AutoAwesomeIcon sx={{ fontSize: 32, color: 'secondary.main' }} />,
      title: 'Smart Monitoring Systems',
      description: 'Developed intelligent systems that learn normal network behavior and automatically alert when anomalies are detected.',
      features: ['Behavioral analysis', 'Anomaly detection', 'Automated alerting']
    },
    {
      icon: <DataObjectIcon sx={{ fontSize: 32, color: 'info.main' }} />,
      title: 'AI APIs & Integration',
      description: 'Integrated ChatGPT and OpenAI APIs to create automated responses, documentation, and intelligent troubleshooting tools.',
      features: ['ChatGPT integration', 'Automated documentation', 'Intelligent responses']
    },
    {
      icon: <TrendingUpIcon sx={{ fontSize: 32, color: 'primary.main' }} />,
      title: 'Machine Learning Analytics',
      description: 'Built ML models for network monitoring, log analysis, and automated report generation using Python and scikit-learn.',
      features: ['Log categorization', 'Network analytics', 'Automated reporting']
    }
  ];

  const codeExample = `# AI-Powered Network Monitoring
import numpy as np
from sklearn.ensemble import IsolationForest
import pandas as pd

class NetworkAnomalyDetector:
    def __init__(self):
        self.model = IsolationForest(contamination=0.1)
        
    def train(self, network_data):
        """Train on normal network patterns"""
        self.model.fit(network_data)
        
    def detect_anomaly(self, current_metrics):
        """Detect if current metrics are anomalous"""
        prediction = self.model.predict([current_metrics])
        return prediction[0] == -1  # -1 indicates anomaly
        
    def alert_admin(self, anomaly_score):
        """Send intelligent alert with context"""
        if anomaly_score < -0.5:
            send_critical_alert()
        else:
            log_minor_anomaly()`;

  return (
    <SectionContainer>
      <Container maxWidth="lg">
        <Stack spacing={8}>
          {/* Header */}
          <Stack spacing={3} textAlign="center">
            <Typography variant="h2" color="text.primary" fontWeight="bold">
              AI & Machine Learning Integration
            </Typography>
            <Typography 
              variant="h6" 
              color="text.secondary" 
              sx={{ maxWidth: 800, mx: 'auto', lineHeight: 1.6 }}
            >
              Leveraging artificial intelligence to revolutionize IT infrastructure management, 
              creating smarter, more efficient, and self-healing systems.
            </Typography>
          </Stack>

          {/* AI Tools Grid */}
          <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
            {aiTools.map((tool, index) => (
              <AICard key={index}>
                <CardContent sx={{ p: 4 }}>
                  <IconWrapper>
                    {tool.icon}
                  </IconWrapper>
                  
                  <Typography variant="h5" fontWeight="bold" gutterBottom>
                    {tool.title}
                  </Typography>
                  
                  <Typography variant="body1" color="text.secondary" paragraph>
                    {tool.description}
                  </Typography>
                  
                  <Stack spacing={1}>
                    {tool.features.map((feature, idx) => (
                      <Typography 
                        key={idx}
                        variant="body2" 
                        color="primary.main"
                        sx={{ 
                          display: 'flex', 
                          alignItems: 'center',
                          '&::before': {
                            content: '"•"',
                            marginRight: 1,
                            fontWeight: 'bold'
                          }
                        }}
                      >
                        {feature}
                      </Typography>
                    ))}
                  </Stack>
                </CardContent>
              </AICard>
            ))}
          </Stack>

          {/* Code Example */}
          <Stack spacing={4}>
            <Typography variant="h4" textAlign="center" fontWeight="bold">
              Real AI Implementation Example
            </Typography>
            
            <CodeBlock>
              <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                {codeExample}
              </pre>
            </CodeBlock>
            
            <Typography variant="body1" color="text.secondary" textAlign="center">
              This Python script demonstrates how I integrate machine learning for network anomaly detection, 
              automatically identifying unusual patterns and alerting administrators with intelligent context.
            </Typography>
          </Stack>

          {/* Career Objective */}
          <Card 
            sx={{ 
              background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(124, 58, 237, 0.1))',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '20px'
            }}
          >
            <CardContent sx={{ p: 6 }}>
              <Typography variant="h4" fontWeight="bold" gutterBottom textAlign="center">
                Career Objective
              </Typography>
              <Typography 
                variant="body1" 
                color="text.secondary" 
                sx={{ lineHeight: 1.8, fontSize: '1.1rem' }}
              >
                Results-driven IT Infrastructure specialist with over 17 years of experience in enterprise environments. 
                Proven expertise in designing, implementing, and maintaining complex IT systems with a focus on high 
                availability and security. Seeking to leverage extensive experience in systems administration, network 
                management, and automation to contribute to a challenging technical leadership role while driving 
                innovation and operational excellence through AI integration.
              </Typography>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    </SectionContainer>
  );
};

export default AISection;