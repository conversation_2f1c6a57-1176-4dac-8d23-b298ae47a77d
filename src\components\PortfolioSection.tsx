import React from 'react';
import { Box, Container, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardContent, <PERSON><PERSON>, Chip } from '@mui/material';
import { styled } from '@mui/material/styles';
import WebIcon from '@mui/icons-material/Web';
import ArticleIcon from '@mui/icons-material/Article';
import CodeIcon from '@mui/icons-material/Code';
import MonitoringIcon from '@mui/icons-material/Monitoring';
import LaunchIcon from '@mui/icons-material/Launch';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(12, 0),
  background: theme.palette.background.default,
}));

const ProjectCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  transition: 'all 0.3s ease',
  height: '100%',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
    background: 'rgba(255, 255, 255, 0.95)',
  },
}));

const BlogCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}10, ${theme.palette.secondary.main}10)`,
  border: `2px solid ${theme.palette.primary.main}30`,
  borderRadius: '20px',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 20px 40px rgba(37, 99, 235, 0.2)',
    background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  width: 64,
  height: 64,
  borderRadius: '16px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  marginBottom: theme.spacing(2),
  boxShadow: '0 10px 30px rgba(37, 99, 235, 0.3)',
}));

const StatsBox = styled(Box)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '12px',
  padding: theme.spacing(2),
  textAlign: 'center',
}));

const PortfolioSection: React.FC = () => {
  const projects = [
    {
      icon: <MonitoringIcon sx={{ fontSize: 32, color: 'white' }} />,
      title: 'Network Monitoring System',
      description: 'Real-time monitoring of all network devices with automated alert system and custom dashboard for network health visualization.',
      technologies: ['Python', 'SNMP', 'Web Dashboard', 'Email/SMS Alerts'],
      features: [
        'Real-time device monitoring',
        'Automated alert system',
        'Custom health dashboard',
        'Integration with notification systems'
      ]
    },
    {
      icon: <WebIcon sx={{ fontSize: 32, color: 'white' }} />,
      title: 'Switch Management Portal',
      description: 'Web-based interface for switch configuration visualization with real-time port status monitoring and VLAN management.',
      technologies: ['Web Interface', 'VLAN Management', 'Real-time Monitoring', 'Configuration Backup'],
      features: [
        'Switch configuration visualization',
        'Real-time port status',
        'VLAN management interface',
        'Connected device tracking'
      ]
    },
    {
      icon: <CodeIcon sx={{ fontSize: 32, color: 'white' }} />,
      title: 'Automated Backup System',
      description: 'Daily configuration backup of all network devices with version control and automated verification of backup success.',
      technologies: ['Automation Scripts', 'Version Control', 'Backup Verification', 'Configuration Management'],
      features: [
        'Daily automated backups',
        'Version control system',
        'Backup verification',
        'Configuration file management'
      ]
    }
  ];

  const blogStats = [
    { label: 'Technical Articles', value: '50+' },
    { label: 'Monthly Readers', value: '10K+' },
    { label: 'Years Active', value: '5+' },
    { label: 'Topics Covered', value: '25+' }
  ];

  return (
    <SectionContainer>
      <Container maxWidth="lg">
        <Stack spacing={8}>
          {/* Header */}
          <Stack spacing={3} textAlign="center">
            <Typography variant="h2" color="text.primary" fontWeight="bold">
              Portfolio & Projects
            </Typography>
            <Typography 
              variant="h6" 
              color="text.secondary" 
              sx={{ maxWidth: 800, mx: 'auto', lineHeight: 1.6 }}
            >
              Custom solutions and knowledge sharing initiatives that demonstrate 
              innovation and commitment to the IT community.
            </Typography>
          </Stack>

          {/* Blog Highlight */}
          <BlogCard>
            <CardContent sx={{ p: 6 }}>
              <Stack spacing={4}>
                <Stack direction={{ xs: 'column', md: 'row' }} spacing={4} alignItems="center">
                  <Stack spacing={3} flex={1}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <ArticleIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                      <Typography variant="h3" fontWeight="bold" color="text.primary">
                        MCSAGuru.com
                      </Typography>
                    </Stack>
                    
                    <Typography variant="h6" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                      Technical blog sharing real-world IT solutions, best practices, and tutorials. 
                      Building a community of IT professionals through comprehensive guides and 
                      practical implementations.
                    </Typography>
                    
                    <Stack direction="row" spacing={2}>
                      <Button
                        variant="contained"
                        startIcon={<LaunchIcon />}
                        href="https://mcsaguru.com"
                        target="_blank"
                        sx={{
                          background: 'linear-gradient(135deg, #2563EB, #7C3AED)',
                          borderRadius: '12px',
                          px: 3,
                          py: 1.5,
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 10px 25px rgba(37, 99, 235, 0.3)',
                          },
                        }}
                      >
                        Visit Blog
                      </Button>
                      
                      <Button
                        variant="outlined"
                        startIcon={<WebIcon />}
                        href="https://portfolio.mcsaguru.com"
                        target="_blank"
                        sx={{
                          borderColor: 'primary.main',
                          color: 'primary.main',
                          borderRadius: '12px',
                          px: 3,
                          py: 1.5,
                          '&:hover': {
                            background: 'rgba(37, 99, 235, 0.1)',
                            transform: 'translateY(-2px)',
                          },
                        }}
                      >
                        Portfolio Site
                      </Button>
                    </Stack>
                  </Stack>
                  
                  <Stack direction={{ xs: 'row', md: 'column' }} spacing={2} flexWrap="wrap">
                    {blogStats.map((stat, index) => (
                      <StatsBox key={index}>
                        <Typography variant="h4" color="primary.main" fontWeight="bold">
                          {stat.value}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {stat.label}
                        </Typography>
                      </StatsBox>
                    ))}
                  </Stack>
                </Stack>

                <Stack spacing={2}>
                  <Typography variant="h6" color="text.primary" fontWeight="bold">
                    Featured Topics
                  </Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                    {[
                      'Windows Server Administration',
                      'Network Infrastructure',
                      'PowerShell Scripting',
                      'VMware Virtualization',
                      'Exchange Server',
                      'Security Best Practices',
                      'Automation Techniques',
                      'Cloud Integration'
                    ].map((topic, index) => (
                      <Chip
                        key={index}
                        label={topic}
                        size="small"
                        sx={{
                          backgroundColor: 'rgba(37, 99, 235, 0.1)',
                          color: 'primary.main',
                          border: '1px solid rgba(37, 99, 235, 0.3)',
                          '&:hover': {
                            backgroundColor: 'rgba(37, 99, 235, 0.2)',
                          }
                        }}
                      />
                    ))}
                  </Stack>
                </Stack>
              </Stack>
            </CardContent>
          </BlogCard>

          {/* Custom Projects */}
          <Stack spacing={4}>
            <Typography variant="h3" textAlign="center" fontWeight="bold">
              Custom Enterprise Solutions
            </Typography>
            
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' },
                gap: 4,
              }}
            >
              {projects.map((project, index) => (
                <ProjectCard key={index}>
                  <CardContent sx={{ p: 4 }}>
                    <Stack spacing={3}>
                      <IconWrapper>
                        {project.icon}
                      </IconWrapper>
                      
                      <Typography variant="h5" fontWeight="bold">
                        {project.title}
                      </Typography>
                      
                      <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                        {project.description}
                      </Typography>
                      
                      <Stack spacing={2}>
                        <Typography variant="h6" color="text.primary" fontWeight="bold">
                          Key Features
                        </Typography>
                        <Stack spacing={1}>
                          {project.features.map((feature, featureIndex) => (
                            <Typography 
                              key={featureIndex}
                              variant="body2" 
                              color="text.secondary"
                              sx={{ 
                                display: 'flex',
                                '&::before': {
                                  content: '"•"',
                                  marginRight: 1,
                                  color: 'primary.main',
                                  fontWeight: 'bold'
                                }
                              }}
                            >
                              {feature}
                            </Typography>
                          ))}
                        </Stack>
                      </Stack>
                      
                      <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                        {project.technologies.map((tech, techIndex) => (
                          <Chip
                            key={techIndex}
                            label={tech}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderColor: 'secondary.main',
                              color: 'secondary.main',
                              '&:hover': {
                                backgroundColor: 'rgba(124, 58, 237, 0.1)',
                              }
                            }}
                          />
                        ))}
                      </Stack>
                    </Stack>
                  </CardContent>
                </ProjectCard>
              ))}
            </Box>
          </Stack>

          {/* Impact Summary */}
          <Card 
            sx={{ 
              background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(37, 99, 235, 0.1))',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '20px'
            }}
          >
            <CardContent sx={{ p: 6 }}>
              <Stack spacing={4} textAlign="center">
                <Typography variant="h4" fontWeight="bold">
                  Knowledge Sharing Impact
                </Typography>
                
                <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 800, mx: 'auto', lineHeight: 1.8 }}>
                  Through MCSAGuru.com and custom enterprise solutions, I've contributed to the IT community 
                  by sharing practical knowledge, real-world implementations, and innovative approaches to 
                  complex technical challenges. This demonstrates not only technical expertise but also 
                  leadership in knowledge transfer and community building.
                </Typography>
                
                <Stack direction={{ xs: 'column', md: 'row' }} spacing={4} justifyContent="center">
                  <Stack spacing={1} alignItems="center">
                    <TrendingUpIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                    <Typography variant="h5" color="primary.main" fontWeight="bold">
                      Community Impact
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Helping IT professionals worldwide
                    </Typography>
                  </Stack>
                  
                  <Stack spacing={1} alignItems="center">
                    <CodeIcon sx={{ fontSize: 40, color: 'secondary.main' }} />
                    <Typography variant="h5" color="secondary.main" fontWeight="bold">
                      Innovation Focus
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Custom solutions for real problems
                    </Typography>
                  </Stack>
                  
                  <Stack spacing={1} alignItems="center">
                    <ArticleIcon sx={{ fontSize: 40, color: 'info.main' }} />
                    <Typography variant="h5" color="info.main" fontWeight="bold">
                      Knowledge Transfer
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Comprehensive documentation
                    </Typography>
                  </Stack>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    </SectionContainer>
  );
};

export default PortfolioSection;