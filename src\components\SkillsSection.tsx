import React, { useState } from 'react';
import { Box, Container, Typography, Stack, Card, CardContent, Chip, LinearProgress } from '@mui/material';
import { styled } from '@mui/material/styles';
import ComputerIcon from '@mui/icons-material/Computer';
import NetworkCheckIcon from '@mui/icons-material/NetworkCheck';
import SecurityIcon from '@mui/icons-material/Security';
import CodeIcon from '@mui/icons-material/Code';
import StorageIcon from '@mui/icons-material/Storage';
import BusinessIcon from '@mui/icons-material/Business';

const SectionContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(12, 0),
  background: theme.palette.background.default,
}));

const SkillCard = styled(Card)<{ isHovered?: boolean }>(({ theme, isHovered }) => ({
  background: isHovered 
    ? 'linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(124, 58, 237, 0.1))'
    : 'rgba(255, 255, 255, 0.8)',
  backdropFilter: 'blur(10px)',
  border: isHovered 
    ? `2px solid ${theme.palette.primary.main}`
    : '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '20px',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  height: '100%',
  transform: isHovered ? 'translateY(-8px) scale(1.02)' : 'translateY(0) scale(1)',
  boxShadow: isHovered 
    ? '0 20px 40px rgba(37, 99, 235, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.1)',
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  width: 80,
  height: 80,
  borderRadius: '20px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  marginBottom: theme.spacing(3),
  boxShadow: '0 10px 30px rgba(37, 99, 235, 0.3)',
}));

const SkillProgress = styled(LinearProgress)(({ theme }) => ({
  height: 8,
  borderRadius: 4,
  backgroundColor: theme.palette.grey[200],
  '& .MuiLinearProgress-bar': {
    borderRadius: 4,
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  },
}));

const SkillsSection: React.FC = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);

  const skillCategories = [
    {
      icon: <ComputerIcon sx={{ fontSize: 40, color: 'white' }} />,
      title: 'Systems Administration',
      level: 95,
      skills: [
        'Windows Server 2012-2022',
        'Ubuntu Linux & CentOS',
        'Active Directory & Group Policy',
        'Exchange Server & Office 365',
        'VMware vSphere & Hyper-V',
        'Veeam Backup & Replication'
      ]
    },
    {
      icon: <NetworkCheckIcon sx={{ fontSize: 40, color: 'white' }} />,
      title: 'Network Infrastructure',
      level: 92,
      skills: [
        'Cisco Catalyst Switches',
        'VLANs & Spanning Tree',
        'OSPF Routing Protocol',
        'Wireless Controllers',
        'TCP/IP & Network Protocols',
        'Load Balancing & HA'
      ]
    },
    {
      icon: <SecurityIcon sx={{ fontSize: 40, color: 'white' }} />,
      title: 'Security & Compliance',
      level: 90,
      skills: [
        'FortiGate Firewalls',
        'VPN Configuration',
        'IPS/IDS Management',
        'Multi-Factor Authentication',
        'Security Auditing',
        'Hikvision Surveillance'
      ]
    },
    {
      icon: <CodeIcon sx={{ fontSize: 40, color: 'white' }} />,
      title: 'Programming & Automation',
      level: 85,
      skills: [
        'PowerShell Scripting',
        'Python & Machine Learning',
        'Bash Automation',
        'REST APIs & JSON',
        'Git Version Control',
        'Task Automation'
      ]
    },
    {
      icon: <StorageIcon sx={{ fontSize: 40, color: 'white' }} />,
      title: 'Database & Storage',
      level: 88,
      skills: [
        'SQL Server Administration',
        'MySQL & MariaDB',
        'Dell EMC Storage',
        'Synology NAS',
        'iSCSI & Fiber Channel',
        'Performance Tuning'
      ]
    },
    {
      icon: <BusinessIcon sx={{ fontSize: 40, color: 'white' }} />,
      title: 'Project Management',
      level: 87,
      skills: [
        'IT Infrastructure Planning',
        'System Implementation',
        'Vendor Management',
        'Technical Documentation',
        'Budget Planning',
        'Risk Management'
      ]
    }
  ];

  return (
    <SectionContainer>
      <Container maxWidth="lg">
        <Stack spacing={8}>
          {/* Header */}
          <Stack spacing={3} textAlign="center">
            <Typography variant="h2" color="text.primary" fontWeight="bold">
              Skills & Technologies
            </Typography>
            <Typography 
              variant="h6" 
              color="text.secondary" 
              sx={{ maxWidth: 800, mx: 'auto', lineHeight: 1.6 }}
            >
              Comprehensive technical expertise across multiple domains, 
              refined through 17+ years of hands-on experience in enterprise environments.
            </Typography>
          </Stack>

          {/* Skills Grid */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)' },
              gap: 4,
            }}
          >
            {skillCategories.map((category, index) => (
              <SkillCard
                key={index}
                isHovered={hoveredCard === index}
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                <CardContent sx={{ p: 4 }}>
                  <Stack spacing={3}>
                    <IconWrapper>
                      {category.icon}
                    </IconWrapper>
                    
                    <Stack spacing={2}>
                      <Typography variant="h5" fontWeight="bold">
                        {category.title}
                      </Typography>
                      
                      <Stack spacing={1}>
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                          <Typography variant="body2" color="text.secondary">
                            Proficiency Level
                          </Typography>
                          <Typography variant="body2" fontWeight="bold" color="primary.main">
                            {category.level}%
                          </Typography>
                        </Stack>
                        <SkillProgress variant="determinate" value={category.level} />
                      </Stack>
                    </Stack>
                    
                    <Stack spacing={1.5}>
                      {category.skills.map((skill, skillIndex) => (
                        <Chip
                          key={skillIndex}
                          label={skill}
                          size="small"
                          sx={{
                            backgroundColor: hoveredCard === index 
                              ? 'rgba(37, 99, 235, 0.1)' 
                              : 'rgba(0, 0, 0, 0.05)',
                            color: hoveredCard === index 
                              ? 'primary.main' 
                              : 'text.secondary',
                            border: hoveredCard === index 
                              ? '1px solid rgba(37, 99, 235, 0.3)' 
                              : '1px solid transparent',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              backgroundColor: 'rgba(37, 99, 235, 0.1)',
                              color: 'primary.main',
                            }
                          }}
                        />
                      ))}
                    </Stack>
                  </Stack>
                </CardContent>
              </SkillCard>
            ))}
          </Box>

          {/* Additional Skills Summary */}
          <Card 
            sx={{ 
              background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(37, 99, 235, 0.1))',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '20px'
            }}
          >
            <CardContent sx={{ p: 6 }}>
              <Typography variant="h4" fontWeight="bold" gutterBottom textAlign="center">
                Additional Expertise
              </Typography>
              
              <Stack direction={{ xs: 'column', md: 'row' }} spacing={4} sx={{ mt: 4 }}>
                <Stack spacing={2} flex={1}>
                  <Typography variant="h6" color="primary.main" fontWeight="bold">
                    Leadership & Soft Skills
                  </Typography>
                  <Stack spacing={1}>
                    {[
                      'Technical Writing & Documentation',
                      'IT Training & Staff Development',
                      'Problem-Solving & Troubleshooting',
                      'Team Leadership',
                      'Change Management',
                      'Disaster Recovery Planning'
                    ].map((skill, index) => (
                      <Typography key={index} variant="body2" color="text.secondary">
                        • {skill}
                      </Typography>
                    ))}
                  </Stack>
                </Stack>
                
                <Stack spacing={2} flex={1}>
                  <Typography variant="h6" color="secondary.main" fontWeight="bold">
                    Monitoring & Tools
                  </Typography>
                  <Stack spacing={1}>
                    {[
                      'PRTG Network Monitor',
                      'Nagios Monitoring',
                      'Custom Monitoring Solutions',
                      'Nmap & Wireshark',
                      'Security Event Management',
                      'Performance Analysis Tools'
                    ].map((skill, index) => (
                      <Typography key={index} variant="body2" color="text.secondary">
                        • {skill}
                      </Typography>
                    ))}
                  </Stack>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    </SectionContainer>
  );
};

export default SkillsSection;